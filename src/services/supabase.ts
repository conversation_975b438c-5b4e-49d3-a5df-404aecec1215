import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase configuration missing. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your environment variables.');
}

// Create Supabase client
export const supabase = createClient(supabaseUrl || '', supabaseAnonKey || '');

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          name: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      forms: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string;
          steps: any; // JSON
          settings: any; // JSON
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          description: string;
          steps: any;
          settings: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          description?: string;
          steps?: any;
          settings?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      form_submissions: {
        Row: {
          id: string;
          form_id: string;
          form_name: string;
          data: any; // JSON
          submitted_at: string;
        };
        Insert: {
          id?: string;
          form_id: string;
          form_name: string;
          data: any;
          submitted_at?: string;
        };
        Update: {
          id?: string;
          form_id?: string;
          form_name?: string;
          data?: any;
          submitted_at?: string;
        };
      };
      integrations: {
        Row: {
          id: string;
          user_id: string;
          provider: string;
          config: any; // JSON (encrypted)
          enabled: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          provider: string;
          config: any;
          enabled?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          provider?: string;
          config?: any;
          enabled?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      analytics_events: {
        Row: {
          id: string;
          form_id: string;
          event_type: string;
          session_id: string;
          timestamp: string;
          metadata: any; // JSON
        };
        Insert: {
          id?: string;
          form_id: string;
          event_type: string;
          session_id: string;
          timestamp?: string;
          metadata?: any;
        };
        Update: {
          id?: string;
          form_id?: string;
          event_type?: string;
          session_id?: string;
          timestamp?: string;
          metadata?: any;
        };
      };
      form_analytics: {
        Row: {
          id: string;
          form_id: string;
          views: number;
          submissions: number;
          conversion_rate: number;
          last_updated: string;
        };
        Insert: {
          id?: string;
          form_id: string;
          views?: number;
          submissions?: number;
          conversion_rate?: number;
          last_updated?: string;
        };
        Update: {
          id?: string;
          form_id?: string;
          views?: number;
          submissions?: number;
          conversion_rate?: number;
          last_updated?: string;
        };
      };
    };
  };
}

// Helper function to check if Supabase is configured
export const isSupabaseConfigured = (): boolean => {
  return !!(supabaseUrl && supabaseAnonKey);
};

// Helper function to get current user
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    console.error('Error getting current user:', error);
    return null;
  }
  return user;
};

// Helper function to sign out
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};
