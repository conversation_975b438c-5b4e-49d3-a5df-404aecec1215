import { useEffect, useRef } from 'react';
import { initializeStores, useAppIntegrationStore, useAppAuthStore } from '../stores/storeManager';
import { isSupabaseConfigured } from '../services/supabase';

/**
 * AppInitializer - Initializes stores and configures integrations
 * This ensures proper setup of both localStorage and Supabase stores
 */
export const AppInitializer = () => {
  const integrationStore = useAppIntegrationStore();
  const { isAuthenticated } = useAppAuthStore();
  const initializationRef = useRef(false);

  useEffect(() => {
    // Prevent multiple initializations
    if (initializationRef.current) return;
    initializationRef.current = true;

    // Initialize all stores
    initializeStores().catch(error => {
      console.error('Failed to initialize stores:', error);
    });
  }, [isAuthenticated]);

  useEffect(() => {
    // Auto-configure HighLevel integration from environment variables
    const envToken = import.meta.env.VITE_HIGHLEVEL_PRIVATE_TOKEN;
    const envLocationId = import.meta.env.VITE_HIGHLEVEL_LOCATION_ID;
    const envWorkflowId = import.meta.env.VITE_HIGHLEVEL_DEFAULT_WORKFLOW_ID;

    // Check if already configured to prevent loops
    const isAlreadyConfigured = integrationStore.isHighLevelEnabled?.() ||
      (typeof integrationStore.isHighLevelEnabled !== 'function' &&
       integrationStore.highlevel?.enabled);

    // Only configure if we have the required credentials and it's not already configured
    if (envToken && envLocationId && !isAlreadyConfigured) {
      console.log('🔧 Auto-configuring HighLevel integration from environment variables');

      try {
        if (isSupabaseConfigured() && isAuthenticated) {
          // Use Supabase store
          integrationStore.configureHighLevel({
            privateIntegrationToken: envToken,
            locationId: envLocationId,
            defaultWorkflowId: envWorkflowId || undefined
          }).then(() => {
            console.log('✅ HighLevel integration auto-configured successfully (Supabase)');
          }).catch(error => {
            console.error('❌ Failed to auto-configure HighLevel integration:', error);
          });
        } else {
          // Use localStorage store
          integrationStore.configureHighLevel({
            privateIntegrationToken: envToken,
            locationId: envLocationId,
            defaultWorkflowId: envWorkflowId || undefined
          });
          console.log('✅ HighLevel integration auto-configured successfully (localStorage)');
        }

        console.log('📋 Location ID:', envLocationId);
        console.log('📋 Token preview:', envToken.substring(0, 10) + '...');
      } catch (error) {
        console.error('❌ Error during HighLevel auto-configuration:', error);
      }
    } else if (isAlreadyConfigured) {
      console.log('✅ HighLevel integration already configured');
    } else if (!envToken || !envLocationId) {
      console.log('⚠️ HighLevel environment variables not found - integration disabled');
    }
  }, []); // Empty dependency array to run only once

  // This component doesn't render anything
  return null;
};

export default AppInitializer;
