import { useEffect, useRef } from 'react';
import { initializeStores, useAppIntegrationStore, useAppAuthStore } from '../stores/storeManager';
import { isSupabaseConfigured, supabase } from '../services/supabase';

/**
 * AppInitializer - Initializes stores and configures integrations
 * This ensures proper setup of both localStorage and Supabase stores
 */
export const AppInitializer = () => {
  const integrationStore = useAppIntegrationStore();
  const { isAuthenticated } = useAppAuthStore();
  const initializationRef = useRef(false);

  useEffect(() => {
    // Prevent multiple initializations
    if (initializationRef.current) return;
    initializationRef.current = true;

    // Initialize all stores
    initializeStores().catch(error => {
      console.error('Failed to initialize stores:', error);
    });

    // Diagnostic: Check database state when authenticated
    if (isAuthenticated && isSupabaseConfigured()) {
      checkDatabaseState();
    }
  }, [isAuthenticated]);

  const checkDatabaseState = async () => {
    try {
      console.log('🔍 Running database diagnostics...');

      // Check current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      console.log('👤 Current user:', user?.id, user?.email);

      if (userError) {
        console.error('❌ User auth error:', userError);
        return;
      }

      if (!user) {
        console.error('❌ No authenticated user found');
        return;
      }

      // Check if user profile exists
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('❌ User profile error:', profileError);
        if (profileError.code === 'PGRST116') {
          console.error('💡 User profile does not exist - this might be the issue!');
        }
      } else {
        console.log('✅ User profile found:', profile);
      }

      // Test basic database operations
      console.log('🧪 Testing database operations...');

      // Test forms table access
      const { data: forms, error: formsError } = await supabase
        .from('forms')
        .select('count')
        .eq('user_id', user.id);

      if (formsError) {
        console.error('❌ Forms access error:', formsError);
      } else {
        console.log('✅ Forms table accessible, count:', forms);
      }

      // Test integrations table access
      const { data: integrations, error: integrationsError } = await supabase
        .from('integrations')
        .select('*')
        .eq('user_id', user.id);

      if (integrationsError) {
        console.error('❌ Integrations access error:', integrationsError);
      } else {
        console.log('✅ Integrations table accessible, found:', integrations?.length || 0);
      }

    } catch (error) {
      console.error('❌ Database diagnostic failed:', error);
    }
  };

  useEffect(() => {
    // Auto-configure HighLevel integration from environment variables
    const envToken = import.meta.env.VITE_HIGHLEVEL_PRIVATE_TOKEN;
    const envLocationId = import.meta.env.VITE_HIGHLEVEL_LOCATION_ID;
    const envWorkflowId = import.meta.env.VITE_HIGHLEVEL_DEFAULT_WORKFLOW_ID;

    // Skip if no environment variables
    if (!envToken || !envLocationId) {
      console.log('⚠️ HighLevel environment variables not found - integration disabled');
      return;
    }

    // Skip if user is not authenticated
    if (!isAuthenticated) {
      console.log('⏳ Waiting for user authentication to configure HighLevel integration');
      return;
    }

    // Check if already configured to prevent loops
    const isAlreadyConfigured = integrationStore.isHighLevelEnabled?.() ||
      (typeof integrationStore.isHighLevelEnabled !== 'function' &&
       integrationStore.highlevel?.enabled);

    if (isAlreadyConfigured) {
      console.log('✅ HighLevel integration already configured');
      return;
    }

    // Only configure if we have the required credentials and it's not already configured
    console.log('🔧 Auto-configuring HighLevel integration from environment variables');

    try {
      if (isSupabaseConfigured()) {
        // Use Supabase store - load integrations first to check if already exists
        integrationStore.loadIntegrations?.().then(() => {
          // Check again after loading
          const stillNeedsConfiguration = !integrationStore.isHighLevelEnabled?.();

          if (stillNeedsConfiguration) {
            return integrationStore.configureHighLevel({
              privateIntegrationToken: envToken,
              locationId: envLocationId,
              defaultWorkflowId: envWorkflowId || undefined
            });
          } else {
            console.log('✅ HighLevel integration already exists in database');
            return Promise.resolve();
          }
        }).then(() => {
          console.log('✅ HighLevel integration auto-configured successfully (Supabase)');
        }).catch(error => {
          // Only log error if it's not a duplicate key constraint
          if (!error.message?.includes('duplicate key') && !error.code?.includes('23505')) {
            console.error('❌ Failed to auto-configure HighLevel integration:', error);
          } else {
            console.log('✅ HighLevel integration already exists (duplicate prevented)');
          }
        });
      } else {
        // Use localStorage store only if Supabase is not configured
        integrationStore.configureHighLevel({
          privateIntegrationToken: envToken,
          locationId: envLocationId,
          defaultWorkflowId: envWorkflowId || undefined
        });
        console.log('✅ HighLevel integration auto-configured successfully (localStorage)');
      }

      console.log('📋 Location ID:', envLocationId);
      console.log('📋 Token preview:', envToken.substring(0, 10) + '...');
    } catch (error) {
      console.error('❌ Error during HighLevel auto-configuration:', error);
    }
  }, [isAuthenticated]); // Depend on authentication status

  // This component doesn't render anything
  return null;
};

export default AppInitializer;
