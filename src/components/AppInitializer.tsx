import { useEffect } from 'react';
import { initializeStores, useAppIntegrationStore, useAppAuthStore } from '../stores/storeManager';
import { isSupabaseConfigured } from '../services/supabase';

/**
 * AppInitializer - Initializes stores and configures integrations
 * This ensures proper setup of both localStorage and Supabase stores
 */
export const AppInitializer = () => {
  const integrationStore = useAppIntegrationStore();
  const { isAuthenticated } = useAppAuthStore();

  useEffect(() => {
    // Initialize all stores
    initializeStores().catch(error => {
      console.error('Failed to initialize stores:', error);
    });
  }, [isAuthenticated]);

  useEffect(() => {
    // Auto-configure HighLevel integration from environment variables
    const envToken = import.meta.env.VITE_HIGHLEVEL_PRIVATE_TOKEN;
    const envLocationId = import.meta.env.VITE_HIGHLEVEL_LOCATION_ID;
    const envWorkflowId = import.meta.env.VITE_HIGHLEVEL_DEFAULT_WORKFLOW_ID;

    // Only configure if we have the required credentials and it's not already configured
    if (envToken && envLocationId && !integrationStore.isHighLevelEnabled()) {
      console.log('🔧 Auto-configuring HighLevel integration from environment variables');

      if (isSupabaseConfigured() && isAuthenticated) {
        // Use Supabase store
        integrationStore.configureHighLevel({
          privateIntegrationToken: envToken,
          locationId: envLocationId,
          defaultWorkflowId: envWorkflowId || undefined
        }).then(() => {
          console.log('✅ HighLevel integration auto-configured successfully (Supabase)');
        }).catch(error => {
          console.error('❌ Failed to auto-configure HighLevel integration:', error);
        });
      } else {
        // Use localStorage store
        integrationStore.configureHighLevel({
          privateIntegrationToken: envToken,
          locationId: envLocationId,
          defaultWorkflowId: envWorkflowId || undefined
        });
        console.log('✅ HighLevel integration auto-configured successfully (localStorage)');
      }

      console.log('📋 Location ID:', envLocationId);
      console.log('📋 Token preview:', envToken.substring(0, 10) + '...');
    } else if (integrationStore.isHighLevelEnabled()) {
      console.log('✅ HighLevel integration already configured');
    } else {
      console.log('⚠️ HighLevel environment variables not found - integration disabled');
    }
  }, [integrationStore, isAuthenticated]);

  // This component doesn't render anything
  return null;
};

export default AppInitializer;
