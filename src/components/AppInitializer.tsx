import { useEffect, useRef } from 'react';
import { initializeStores, useAppIntegrationStore, useAppAuthStore } from '../stores/storeManager';
import { isSupabaseConfigured } from '../services/supabase';

/**
 * AppInitializer - Initializes stores and configures integrations
 * This ensures proper setup of both localStorage and Supabase stores
 */
export const AppInitializer = () => {
  const integrationStore = useAppIntegrationStore();
  const { isAuthenticated } = useAppAuthStore();
  const initializationRef = useRef(false);

  useEffect(() => {
    // Prevent multiple initializations
    if (initializationRef.current) return;
    initializationRef.current = true;

    // Initialize all stores
    initializeStores().catch(error => {
      console.error('Failed to initialize stores:', error);
    });
  }, [isAuthenticated]);

  useEffect(() => {
    // Auto-configure HighLevel integration from environment variables
    const envToken = import.meta.env.VITE_HIGHLEVEL_PRIVATE_TOKEN;
    const envLocationId = import.meta.env.VITE_HIGHLEVEL_LOCATION_ID;
    const envWorkflowId = import.meta.env.VITE_HIGHLEVEL_DEFAULT_WORKFLOW_ID;

    // Skip if no environment variables
    if (!envToken || !envLocationId) {
      console.log('⚠️ HighLevel environment variables not found - integration disabled');
      return;
    }

    // Skip if user is not authenticated
    if (!isAuthenticated) {
      console.log('⏳ Waiting for user authentication to configure HighLevel integration');
      return;
    }

    // Check if already configured to prevent loops
    const isAlreadyConfigured = integrationStore.isHighLevelEnabled?.() ||
      (typeof integrationStore.isHighLevelEnabled !== 'function' &&
       integrationStore.highlevel?.enabled);

    if (isAlreadyConfigured) {
      console.log('✅ HighLevel integration already configured');
      return;
    }

    // Only configure if we have the required credentials and it's not already configured
    console.log('🔧 Auto-configuring HighLevel integration from environment variables');

    try {
      if (isSupabaseConfigured()) {
        // Use Supabase store - load integrations first to check if already exists
        integrationStore.loadIntegrations?.().then(() => {
          // Check again after loading
          const stillNeedsConfiguration = !integrationStore.isHighLevelEnabled?.();

          if (stillNeedsConfiguration) {
            return integrationStore.configureHighLevel({
              privateIntegrationToken: envToken,
              locationId: envLocationId,
              defaultWorkflowId: envWorkflowId || undefined
            });
          } else {
            console.log('✅ HighLevel integration already exists in database');
            return Promise.resolve();
          }
        }).then(() => {
          console.log('✅ HighLevel integration auto-configured successfully (Supabase)');
        }).catch(error => {
          // Only log error if it's not a duplicate key constraint
          if (!error.message?.includes('duplicate key') && !error.code?.includes('23505')) {
            console.error('❌ Failed to auto-configure HighLevel integration:', error);
          } else {
            console.log('✅ HighLevel integration already exists (duplicate prevented)');
          }
        });
      } else {
        // Use localStorage store only if Supabase is not configured
        integrationStore.configureHighLevel({
          privateIntegrationToken: envToken,
          locationId: envLocationId,
          defaultWorkflowId: envWorkflowId || undefined
        });
        console.log('✅ HighLevel integration auto-configured successfully (localStorage)');
      }

      console.log('📋 Location ID:', envLocationId);
      console.log('📋 Token preview:', envToken.substring(0, 10) + '...');
    } catch (error) {
      console.error('❌ Error during HighLevel auto-configuration:', error);
    }
  }, [isAuthenticated]); // Depend on authentication status

  // This component doesn't render anything
  return null;
};

export default AppInitializer;
