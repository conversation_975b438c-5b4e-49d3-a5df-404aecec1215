import React, { useState } from 'react';
import { AlertCircle, Database, CheckCircle, ArrowRight, Loader2 } from 'lucide-react';
import { migrateToSupabase, isMigrationNeeded, getStoreStatus } from '../stores/storeManager';
import { isSupabaseConfigured } from '../services/supabase';

interface SupabaseMigrationProps {
  onMigrationComplete?: () => void;
}

export const SupabaseMigration: React.FC<SupabaseMigrationProps> = ({ onMigrationComplete }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const storeStatus = getStoreStatus();
  const migrationNeeded = isMigrationNeeded();

  const handleMigration = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await migrateToSupabase();
      setSuccess(true);
      onMigrationComplete?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Migration failed');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isSupabaseConfigured()) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-blue-800">Supabase Not Configured</h3>
            <p className="text-sm text-blue-700 mt-1">
              To enable persistent backend storage, please configure Supabase by following the setup guide.
            </p>
            <a 
              href="/SUPABASE_SETUP.md" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:text-blue-800 underline mt-2 inline-block"
            >
              View Supabase Setup Guide →
            </a>
          </div>
        </div>
      </div>
    );
  }

  if (!migrationNeeded && !success) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start">
          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-green-800">Backend Ready</h3>
            <p className="text-sm text-green-700 mt-1">
              Your application is using Supabase backend for persistent storage.
            </p>
            <div className="mt-2 text-xs text-green-600">
              <div>✅ Supabase configured</div>
              <div>✅ User authenticated</div>
              <div>✅ Data synchronized</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start">
          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-green-800">Migration Completed!</h3>
            <p className="text-sm text-green-700 mt-1">
              Your data has been successfully migrated to Supabase. Your forms, leads, and settings are now stored securely in the cloud.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="mt-3 text-sm bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700"
            >
              Refresh Application
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-start">
        <Database className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800">Data Migration Available</h3>
          <p className="text-sm text-yellow-700 mt-1">
            We detected existing data in your browser storage. Migrate it to Supabase for persistent, secure storage across devices.
          </p>
          
          <div className="mt-3 space-y-2">
            <div className="flex items-center text-xs text-yellow-600">
              <span className="w-20">Browser Storage</span>
              <ArrowRight className="h-3 w-3 mx-2" />
              <span>Supabase Database</span>
            </div>
            
            <div className="text-xs text-yellow-600 space-y-1">
              <div>• Forms and form configurations</div>
              <div>• Lead submissions and data</div>
              <div>• Integration settings (HighLevel)</div>
              <div>• Analytics data</div>
            </div>
          </div>

          {error && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              <strong>Migration Error:</strong> {error}
            </div>
          )}

          <div className="mt-4 flex space-x-3">
            <button
              onClick={handleMigration}
              disabled={isLoading}
              className="flex items-center text-sm bg-yellow-600 text-white px-3 py-2 rounded hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Migrating...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  Migrate to Supabase
                </>
              )}
            </button>
            
            <button
              onClick={() => {
                if (confirm('Are you sure you want to skip migration? Your local data will remain in browser storage only.')) {
                  onMigrationComplete?.();
                }
              }}
              className="text-sm text-yellow-600 hover:text-yellow-800 px-3 py-2"
            >
              Skip Migration
            </button>
          </div>

          <div className="mt-3 text-xs text-yellow-600">
            <strong>Note:</strong> Migration is safe and non-destructive. Your browser data will remain as backup.
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupabaseMigration;
