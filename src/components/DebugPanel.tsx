import React, { useState } from 'react';
import { Bug, ChevronDown, ChevronUp } from 'lucide-react';
import { getStoreStatus, isMigrationNeeded } from '../stores/storeManager';
import { isSupabaseConfigured } from '../services/supabase';

export const DebugPanel: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const storeStatus = getStoreStatus();

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-gray-800 text-white rounded-lg shadow-lg">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-3 py-2 text-sm hover:bg-gray-700 rounded-lg"
        >
          <Bug className="h-4 w-4" />
          <span>Debug</span>
          {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </button>
        
        {isOpen && (
          <div className="p-4 border-t border-gray-700 min-w-80">
            <h3 className="text-sm font-semibold mb-3">Store Status</h3>
            
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span>Supabase Configured:</span>
                <span className={storeStatus.supabaseConfigured ? 'text-green-400' : 'text-red-400'}>
                  {storeStatus.supabaseConfigured ? '✅' : '❌'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span>User Authenticated:</span>
                <span className={storeStatus.userAuthenticated ? 'text-green-400' : 'text-red-400'}>
                  {storeStatus.userAuthenticated ? '✅' : '❌'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span>Using Supabase Stores:</span>
                <span className={storeStatus.usingSupabaseStores ? 'text-green-400' : 'text-yellow-400'}>
                  {storeStatus.usingSupabaseStores ? '✅' : '📦'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span>Migration Needed:</span>
                <span className={storeStatus.migrationNeeded ? 'text-yellow-400' : 'text-green-400'}>
                  {storeStatus.migrationNeeded ? '⚠️' : '✅'}
                </span>
              </div>
            </div>

            <div className="mt-4 pt-3 border-t border-gray-700">
              <h4 className="text-sm font-semibold mb-2">Environment</h4>
              <div className="space-y-1 text-xs">
                <div>
                  <span className="text-gray-400">Supabase URL:</span>
                  <div className="font-mono text-xs break-all">
                    {import.meta.env.VITE_SUPABASE_URL || 'Not set'}
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">Supabase Key:</span>
                  <div className="font-mono text-xs">
                    {import.meta.env.VITE_SUPABASE_ANON_KEY 
                      ? `${import.meta.env.VITE_SUPABASE_ANON_KEY.substring(0, 20)}...` 
                      : 'Not set'}
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">HighLevel Token:</span>
                  <div className="font-mono text-xs">
                    {import.meta.env.VITE_HIGHLEVEL_PRIVATE_TOKEN 
                      ? `${import.meta.env.VITE_HIGHLEVEL_PRIVATE_TOKEN.substring(0, 10)}...` 
                      : 'Not set'}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 pt-3 border-t border-gray-700">
              <h4 className="text-sm font-semibold mb-2">Actions</h4>
              <div className="space-y-2">
                <button
                  onClick={() => {
                    localStorage.clear();
                    sessionStorage.clear();
                    window.location.reload();
                  }}
                  className="w-full text-xs bg-red-600 hover:bg-red-700 px-2 py-1 rounded"
                >
                  Clear All Storage & Reload
                </button>
                
                <button
                  onClick={() => {
                    console.log('Store Status:', getStoreStatus());
                    console.log('Migration Needed:', isMigrationNeeded());
                    console.log('Supabase Configured:', isSupabaseConfigured());
                  }}
                  className="w-full text-xs bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded"
                >
                  Log Debug Info
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DebugPanel;
