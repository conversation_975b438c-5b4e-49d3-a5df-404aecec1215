import { isSupabaseConfigured } from '../services/supabase';

// Import localStorage-based stores
import { useAuthStore } from './authStore';
import { useFormStore } from './formStore';
import { useLeadsStore } from './leadsStore';
import { useAnalyticsStore } from './analyticsStore';
import { useIntegrationStore } from './integrationStore';

// Import Supabase-based stores
import { useSupabaseFormStore } from './supabaseFormStore';
import { useSupabaseLeadsStore } from './supabaseLeadsStore';
import { useSupabaseAnalyticsStore } from './supabaseAnalyticsStore';
import { useSupabaseIntegrationStore } from './supabaseIntegrationStore';

/**
 * Store Manager - Provides a unified interface to switch between
 * localStorage-based stores and Supabase-based stores depending on configuration
 */

// Auth store (always uses the same store with Supabase fallback)
export const useAppAuthStore = useAuthStore;

// Form store
export const useAppFormStore = () => {
  if (isSupabaseConfigured()) {
    return useSupabaseFormStore();
  }
  return useFormStore();
};

// Leads store
export const useAppLeadsStore = () => {
  if (isSupabaseConfigured()) {
    return useSupabaseLeadsStore();
  }
  return useLeadsStore();
};

// Analytics store
export const useAppAnalyticsStore = () => {
  if (isSupabaseConfigured()) {
    return useSupabaseAnalyticsStore();
  }
  return useAnalyticsStore();
};

// Integration store
export const useAppIntegrationStore = () => {
  if (isSupabaseConfigured()) {
    return useSupabaseIntegrationStore();
  }
  return useIntegrationStore();
};

/**
 * Initialize all stores - call this when the app starts
 */
export const initializeStores = async () => {
  try {
    // Initialize auth first
    const authStore = useAppAuthStore.getState();
    if (authStore.initializeAuth) {
      await authStore.initializeAuth();
    }

    // Only initialize other stores if user is authenticated and Supabase is configured
    if (isSupabaseConfigured() && authStore.isAuthenticated) {
      // Initialize Supabase stores
      const formStore = useSupabaseFormStore.getState();
      const leadsStore = useSupabaseLeadsStore.getState();
      const analyticsStore = useSupabaseAnalyticsStore.getState();
      const integrationStore = useSupabaseIntegrationStore.getState();

      // Load data from Supabase
      await Promise.all([
        formStore.loadForms(),
        leadsStore.loadLeads(),
        analyticsStore.loadFormAnalytics(),
        integrationStore.loadIntegrations(),
      ]);

      console.log('✅ Supabase stores initialized successfully');
    } else {
      console.log('📦 Using localStorage stores (Supabase not configured or user not authenticated)');
    }
  } catch (error) {
    console.error('❌ Error initializing stores:', error);
  }
};

/**
 * Migration helper - migrate data from localStorage to Supabase
 * Call this when user first configures Supabase
 */
export const migrateToSupabase = async () => {
  if (!isSupabaseConfigured()) {
    throw new Error('Supabase is not configured');
  }

  const authStore = useAppAuthStore.getState();
  if (!authStore.isAuthenticated) {
    throw new Error('User must be authenticated to migrate data');
  }

  try {
    console.log('🔄 Starting migration to Supabase...');

    // Get data from localStorage stores
    const localFormStore = useFormStore.getState();
    const localLeadsStore = useLeadsStore.getState();
    const localIntegrationStore = useIntegrationStore.getState();

    // Get Supabase stores
    const supabaseFormStore = useSupabaseFormStore.getState();
    const supabaseLeadsStore = useSupabaseLeadsStore.getState();
    const supabaseIntegrationStore = useSupabaseIntegrationStore.getState();

    // Migrate forms
    console.log('📋 Migrating forms...');
    for (const form of localFormStore.forms) {
      try {
        await supabaseFormStore.createForm(form.name, form.description);
        // Note: This creates a basic form. You might want to update it with full data
        const createdFormId = supabaseFormStore.forms[supabaseFormStore.forms.length - 1]?.id;
        if (createdFormId) {
          await supabaseFormStore.updateForm(createdFormId, {
            steps: form.steps,
            settings: form.settings,
          });
        }
      } catch (error) {
        console.warn(`Failed to migrate form ${form.name}:`, error);
      }
    }

    // Migrate leads (this is tricky since we need to match form IDs)
    console.log('👥 Migrating leads...');
    for (const lead of localLeadsStore.leads) {
      try {
        // Find corresponding form in Supabase by name
        const matchingForm = supabaseFormStore.forms.find(f => f.name === lead.formName);
        if (matchingForm) {
          await supabaseLeadsStore.addLead(matchingForm.id, lead.formName, lead.data);
        }
      } catch (error) {
        console.warn(`Failed to migrate lead for form ${lead.formName}:`, error);
      }
    }

    // Migrate HighLevel integration
    console.log('🔗 Migrating integrations...');
    if (localIntegrationStore.highlevel.enabled) {
      try {
        await supabaseIntegrationStore.configureHighLevel({
          privateIntegrationToken: localIntegrationStore.highlevel.privateIntegrationToken,
          locationId: localIntegrationStore.highlevel.locationId,
          defaultWorkflowId: localIntegrationStore.highlevel.defaultWorkflowId,
        });
      } catch (error) {
        console.warn('Failed to migrate HighLevel integration:', error);
      }
    }

    console.log('✅ Migration to Supabase completed successfully');
    
    // Optionally clear localStorage data after successful migration
    // localStorage.removeItem('forms-storage');
    // localStorage.removeItem('leads-storage');
    // localStorage.removeItem('integration-storage');
    // localStorage.removeItem('analytics-storage');

  } catch (error) {
    console.error('❌ Migration to Supabase failed:', error);
    throw error;
  }
};

/**
 * Check if migration is needed
 */
export const isMigrationNeeded = (): boolean => {
  if (!isSupabaseConfigured()) {
    return false;
  }

  const authStore = useAppAuthStore.getState();
  if (!authStore.isAuthenticated) {
    return false;
  }

  // Check if there's data in localStorage but not in Supabase
  const localFormStore = useFormStore.getState();
  const supabaseFormStore = useSupabaseFormStore.getState();

  return localFormStore.forms.length > 0 && supabaseFormStore.forms.length === 0;
};

/**
 * Get store status for debugging
 */
export const getStoreStatus = () => {
  const authStore = useAppAuthStore.getState();
  
  return {
    supabaseConfigured: isSupabaseConfigured(),
    userAuthenticated: authStore.isAuthenticated,
    usingSupabaseStores: isSupabaseConfigured() && authStore.isAuthenticated,
    migrationNeeded: isMigrationNeeded(),
  };
};
