import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { supabase, isSupabaseConfigured, getCurrentUser, signOut } from '../services/supabase';

interface User {
  id: string;
  email: string;
  name: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  updateProfile: (updates: Partial<User>) => void;
  logout: () => void;
}

// For demo purposes, we're using mock data with localStorage persistence
// In a real app, this would connect to a backend API
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

  login: async (email: string, password: string) => {
    set({ isLoading: true });

    try {
      if (!isSupabaseConfigured()) {
        // Fallback to mock authentication if Supabase is not configured
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (email && password) {
          set({
            isAuthenticated: true,
            user: {
              id: '1',
              email,
              name: 'Demo User'
            },
            isLoading: false
          });
        } else {
          throw new Error('Invalid credentials');
        }
        return;
      }

      // Use Supabase authentication
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        // Get or create user profile
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          console.error('Error fetching user profile:', profileError);
        }

        set({
          isAuthenticated: true,
          user: {
            id: data.user.id,
            email: data.user.email || email,
            name: profile?.name || data.user.user_metadata?.name || 'User'
          },
          isLoading: false
        });
      }
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  register: async (name: string, email: string, password: string) => {
    set({ isLoading: true });

    try {
      if (!isSupabaseConfigured()) {
        // Fallback to mock authentication if Supabase is not configured
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (name && email && password) {
          set({
            isAuthenticated: true,
            user: {
              id: '1',
              email,
              name
            },
            isLoading: false
          });
        } else {
          throw new Error('Invalid registration data');
        }
        return;
      }

      // Use Supabase authentication
      console.log('Attempting to register user:', { email, name });

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name,
          }
        }
      });

      console.log('Supabase signup response:', { data, error });

      if (error) {
        console.error('Supabase signup error:', error);
        throw error;
      }

      if (data.user) {
        // User profile will be created automatically by the trigger
        set({
          isAuthenticated: !!data.session,
          user: data.session ? {
            id: data.user.id,
            email: data.user.email || email,
            name: name
          } : null,
          isLoading: false
        });

        if (!data.session) {
          // Email confirmation required
          throw new Error('Please check your email to confirm your account');
        }
      }
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  updateProfile: (updates: Partial<User>) => {
    const currentUser = get().user;
    if (currentUser) {
      set({
        user: { ...currentUser, ...updates }
      });
    }
  },

  logout: async () => {
    try {
      if (isSupabaseConfigured()) {
        await signOut();
      }
      set({ user: null, isAuthenticated: false });
    } catch (error) {
      console.error('Error during logout:', error);
      // Still clear local state even if Supabase logout fails
      set({ user: null, isAuthenticated: false });
    }
  },

  // Initialize auth state from Supabase session
  initializeAuth: async () => {
    if (!isSupabaseConfigured()) {
      return;
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        // Get user profile
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();

        set({
          isAuthenticated: true,
          user: {
            id: session.user.id,
            email: session.user.email || '',
            name: profile?.name || session.user.user_metadata?.name || 'User'
          }
        });
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
    }
  }
    }),
    {
      name: 'auth-storage', // localStorage key
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      }), // Persist user and auth status
    }
  )
);