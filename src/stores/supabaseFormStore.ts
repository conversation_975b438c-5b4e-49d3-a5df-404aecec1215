import { create } from 'zustand';
import { supabase, isSupabaseConfigured } from '../services/supabase';
import { v4 as uuidv4 } from 'uuid';

// Import types from the original form store
import type { Form, FormStep, Question } from './formStore';

interface SupabaseFormState {
  forms: Form[];
  currentForm: Form | null;
  isLoading: boolean;
  error: string | null;

  // Form management
  createForm: (name: string, description: string) => Promise<string>;
  updateForm: (id: string, updates: Partial<Form>) => Promise<void>;
  deleteForm: (id: string) => Promise<void>;
  duplicateForm: (id: string) => Promise<string>;
  loadForms: () => Promise<void>;
  setCurrentForm: (form: Form | null) => void;

  // Step management
  addStep: (formId: string) => Promise<void>;
  updateStep: (formId: string, stepId: string, updates: Partial<FormStep>) => Promise<void>;
  deleteStep: (formId: string, stepId: string) => Promise<void>;
  reorderSteps: (formId: string, stepIds: string[]) => Promise<void>;

  // Question management
  addQuestion: (formId: string, stepId: string, question: Omit<Question, 'id'>) => Promise<void>;
  updateQuestion: (formId: string, stepId: string, questionId: string, updates: Partial<Question>) => Promise<void>;
  deleteQuestion: (formId: string, stepId: string, questionId: string) => Promise<void>;
  duplicateQuestion: (formId: string, stepId: string, questionId: string) => Promise<void>;
  reorderQuestions: (formId: string, stepId: string, questionIds: string[]) => Promise<void>;

  // Settings
  updateFormSettings: (formId: string, settings: any) => Promise<void>;

  // Clear state
  clearError: () => void;
}

export const useSupabaseFormStore = create<SupabaseFormState>((set, get) => ({
  forms: [],
  currentForm: null,
  isLoading: false,
  error: null,

  createForm: async (name: string, description: string) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const newForm: Omit<Form, 'id' | 'createdAt' | 'updatedAt'> = {
        name,
        description,
        steps: [
          {
            id: uuidv4(),
            title: 'Step 1',
            questions: [],
          }
        ],
        settings: {
          primaryColor: '#3B82F6',
          showProgressBar: true,
          thankYouMessage: 'Thank you for your submission!',
        }
      };

      const { data, error } = await supabase
        .from('forms')
        .insert({
          user_id: user.id,
          name: newForm.name,
          description: newForm.description,
          steps: newForm.steps,
          settings: newForm.settings,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const form: Form = {
        id: data.id,
        name: data.name,
        description: data.description,
        steps: data.steps,
        settings: data.settings,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      set(state => ({
        forms: [...state.forms, form],
        currentForm: form,
        isLoading: false,
      }));

      return data.id;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create form';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  updateForm: async (id: string, updates: Partial<Form>) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    set({ isLoading: true, error: null });

    try {
      const updateData: any = {};
      if (updates.name) updateData.name = updates.name;
      if (updates.description) updateData.description = updates.description;
      if (updates.steps) updateData.steps = updates.steps;
      if (updates.settings) updateData.settings = updates.settings;

      const { data, error } = await supabase
        .from('forms')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      const updatedForm: Form = {
        id: data.id,
        name: data.name,
        description: data.description,
        steps: data.steps,
        settings: data.settings,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      set(state => ({
        forms: state.forms.map(form => form.id === id ? updatedForm : form),
        currentForm: state.currentForm?.id === id ? updatedForm : state.currentForm,
        isLoading: false,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update form';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  deleteForm: async (id: string) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase
        .from('forms')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      set(state => ({
        forms: state.forms.filter(form => form.id !== id),
        currentForm: state.currentForm?.id === id ? null : state.currentForm,
        isLoading: false,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete form';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  duplicateForm: async (id: string) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    const originalForm = get().forms.find(form => form.id === id);
    if (!originalForm) {
      throw new Error('Form not found');
    }

    return await get().createForm(
      `${originalForm.name} (Copy)`,
      originalForm.description
    );
  },

  loadForms: async () => {
    if (!isSupabaseConfigured()) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false });
        return;
      }

      const { data, error } = await supabase
        .from('forms')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      const forms: Form[] = data.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        steps: item.steps,
        settings: item.settings,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
      }));

      set({ forms, isLoading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load forms';
      set({ error: errorMessage, isLoading: false });
    }
  },

  setCurrentForm: (form: Form | null) => {
    set({ currentForm: form });
  },

  // Step management methods
  addStep: async (formId: string) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const newStep: FormStep = {
      id: uuidv4(),
      title: `Step ${form.steps.length + 1}`,
      questions: [],
    };

    const updatedSteps = [...form.steps, newStep];
    await get().updateForm(formId, { steps: updatedSteps });
  },

  updateStep: async (formId: string, stepId: string, updates: Partial<FormStep>) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const updatedSteps = form.steps.map(step =>
      step.id === stepId ? { ...step, ...updates } : step
    );

    await get().updateForm(formId, { steps: updatedSteps });
  },

  deleteStep: async (formId: string, stepId: string) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form || form.steps.length <= 1) return;

    const updatedSteps = form.steps.filter(step => step.id !== stepId);
    await get().updateForm(formId, { steps: updatedSteps });
  },

  reorderSteps: async (formId: string, stepIds: string[]) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const reorderedSteps = stepIds.map(id => 
      form.steps.find(step => step.id === id)!
    ).filter(Boolean);

    await get().updateForm(formId, { steps: reorderedSteps });
  },

  // Question management methods
  addQuestion: async (formId: string, stepId: string, question: Omit<Question, 'id'>) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const newQuestion: Question = {
      ...question,
      id: uuidv4(),
    };

    const updatedSteps = form.steps.map(step =>
      step.id === stepId
        ? { ...step, questions: [...step.questions, newQuestion] }
        : step
    );

    await get().updateForm(formId, { steps: updatedSteps });
  },

  updateQuestion: async (formId: string, stepId: string, questionId: string, updates: Partial<Question>) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const updatedSteps = form.steps.map(step =>
      step.id === stepId
        ? {
            ...step,
            questions: step.questions.map(q =>
              q.id === questionId ? { ...q, ...updates } : q
            )
          }
        : step
    );

    await get().updateForm(formId, { steps: updatedSteps });
  },

  deleteQuestion: async (formId: string, stepId: string, questionId: string) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const updatedSteps = form.steps.map(step =>
      step.id === stepId
        ? {
            ...step,
            questions: step.questions.filter(q => q.id !== questionId)
          }
        : step
    );

    await get().updateForm(formId, { steps: updatedSteps });
  },

  duplicateQuestion: async (formId: string, stepId: string, questionId: string) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const step = form.steps.find(s => s.id === stepId);
    if (!step) return;

    const originalQuestion = step.questions.find(q => q.id === questionId);
    if (!originalQuestion) return;

    const duplicatedQuestion: Question = {
      ...originalQuestion,
      id: uuidv4(),
      label: `${originalQuestion.label} (Copy)`,
    };

    const updatedSteps = form.steps.map(s =>
      s.id === stepId
        ? {
            ...s,
            questions: [...s.questions, duplicatedQuestion]
          }
        : s
    );

    await get().updateForm(formId, { steps: updatedSteps });
  },

  reorderQuestions: async (formId: string, stepId: string, questionIds: string[]) => {
    const form = get().forms.find(f => f.id === formId);
    if (!form) return;

    const step = form.steps.find(s => s.id === stepId);
    if (!step) return;

    const reorderedQuestions = questionIds.map(id =>
      step.questions.find(q => q.id === id)!
    ).filter(Boolean);

    const updatedSteps = form.steps.map(s =>
      s.id === stepId
        ? { ...s, questions: reorderedQuestions }
        : s
    );

    await get().updateForm(formId, { steps: updatedSteps });
  },

  updateFormSettings: async (formId: string, settings: any) => {
    await get().updateForm(formId, { settings });
  },

  clearError: () => {
    set({ error: null });
  },
}));
