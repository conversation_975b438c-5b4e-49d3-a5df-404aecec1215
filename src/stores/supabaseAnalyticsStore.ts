import { create } from 'zustand';
import { supabase, isSupabaseConfigured } from '../services/supabase';

// Import types from the original analytics store
import type { AnalyticsEvent, FormAnalytics } from './analyticsStore';

interface SupabaseAnalyticsState {
  events: AnalyticsEvent[];
  formAnalytics: Record<string, FormAnalytics>;
  isLoading: boolean;
  error: string | null;

  // Event tracking
  trackFormView: (formId: string) => Promise<void>;
  trackFormSubmission: (formId: string, submissionId?: string) => Promise<void>;
  trackStepCompletion: (formId: string, stepId: string) => Promise<void>;

  // Analytics data
  loadFormAnalytics: (formId?: string) => Promise<void>;
  getFormAnalytics: (formId: string) => FormAnalytics | null;

  // Clear state
  clearError: () => void;
}

export const useSupabaseAnalyticsStore = create<SupabaseAnalyticsState>((set, get) => ({
  events: [],
  formAnalytics: {},
  isLoading: false,
  error: null,

  trackFormView: async (formId: string) => {
    if (!isSupabaseConfigured()) {
      // Fallback to local tracking if Supabase is not configured
      const event: AnalyticsEvent = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        formId,
        eventType: 'form_view',
        timestamp: new Date(),
        sessionId: getSessionId(),
      };

      set(state => ({
        events: [...state.events, event]
      }));
      return;
    }

    try {
      const { data, error } = await supabase
        .from('analytics_events')
        .insert({
          form_id: formId,
          event_type: 'form_view',
          session_id: getSessionId(),
          metadata: {
            timestamp: new Date().toISOString(),
            user_agent: navigator.userAgent,
            referrer: document.referrer,
          }
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const event: AnalyticsEvent = {
        id: data.id,
        formId: data.form_id,
        eventType: data.event_type as 'form_view',
        timestamp: new Date(data.timestamp),
        sessionId: data.session_id,
        metadata: data.metadata,
      };

      set(state => ({
        events: [...state.events, event]
      }));

    } catch (error) {
      console.error('Failed to track form view:', error);
      // Continue with local tracking as fallback
      const event: AnalyticsEvent = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        formId,
        eventType: 'form_view',
        timestamp: new Date(),
        sessionId: getSessionId(),
      };

      set(state => ({
        events: [...state.events, event]
      }));
    }
  },

  trackFormSubmission: async (formId: string, submissionId?: string) => {
    if (!isSupabaseConfigured()) {
      // Fallback to local tracking if Supabase is not configured
      const event: AnalyticsEvent = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        formId,
        eventType: 'form_submission',
        timestamp: new Date(),
        sessionId: getSessionId(),
      };

      set(state => ({
        events: [...state.events, event]
      }));
      return;
    }

    try {
      const { data, error } = await supabase
        .from('analytics_events')
        .insert({
          form_id: formId,
          event_type: 'form_submission',
          session_id: getSessionId(),
          metadata: {
            submission_id: submissionId,
            timestamp: new Date().toISOString(),
          }
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const event: AnalyticsEvent = {
        id: data.id,
        formId: data.form_id,
        eventType: data.event_type as 'form_submission',
        timestamp: new Date(data.timestamp),
        sessionId: data.session_id,
        metadata: data.metadata,
      };

      set(state => ({
        events: [...state.events, event]
      }));

    } catch (error) {
      console.error('Failed to track form submission:', error);
      // Continue with local tracking as fallback
      const event: AnalyticsEvent = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        formId,
        eventType: 'form_submission',
        timestamp: new Date(),
        sessionId: getSessionId(),
      };

      set(state => ({
        events: [...state.events, event]
      }));
    }
  },

  trackStepCompletion: async (formId: string, stepId: string) => {
    if (!isSupabaseConfigured()) {
      // Fallback to local tracking if Supabase is not configured
      const event: AnalyticsEvent = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        formId,
        eventType: 'step_completion',
        timestamp: new Date(),
        sessionId: getSessionId(),
        metadata: { stepId },
      };

      set(state => ({
        events: [...state.events, event]
      }));
      return;
    }

    try {
      const { data, error } = await supabase
        .from('analytics_events')
        .insert({
          form_id: formId,
          event_type: 'step_completion',
          session_id: getSessionId(),
          metadata: {
            step_id: stepId,
            timestamp: new Date().toISOString(),
          }
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const event: AnalyticsEvent = {
        id: data.id,
        formId: data.form_id,
        eventType: data.event_type as 'step_completion',
        timestamp: new Date(data.timestamp),
        sessionId: data.session_id,
        metadata: data.metadata,
      };

      set(state => ({
        events: [...state.events, event]
      }));

    } catch (error) {
      console.error('Failed to track step completion:', error);
      // Continue with local tracking as fallback
      const event: AnalyticsEvent = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        formId,
        eventType: 'step_completion',
        timestamp: new Date(),
        sessionId: getSessionId(),
        metadata: { stepId },
      };

      set(state => ({
        events: [...state.events, event]
      }));
    }
  },

  loadFormAnalytics: async (formId?: string) => {
    if (!isSupabaseConfigured()) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false });
        return;
      }

      let query = supabase
        .from('form_analytics')
        .select(`
          *,
          forms!inner(user_id)
        `)
        .eq('forms.user_id', user.id);

      if (formId) {
        query = query.eq('form_id', formId);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      const analytics: Record<string, FormAnalytics> = {};
      
      data.forEach(item => {
        analytics[item.form_id] = {
          formId: item.form_id,
          views: item.views,
          submissions: item.submissions,
          conversionRate: parseFloat(item.conversion_rate),
          lastUpdated: new Date(item.last_updated),
        };
      });

      set(state => ({
        formAnalytics: formId 
          ? { ...state.formAnalytics, [formId]: analytics[formId] }
          : analytics,
        isLoading: false,
      }));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load analytics';
      set({ error: errorMessage, isLoading: false });
    }
  },

  getFormAnalytics: (formId: string) => {
    return get().formAnalytics[formId] || null;
  },

  clearError: () => {
    set({ error: null });
  },
}));

// Helper function to get or create session ID
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
};

// Helper function to generate session ID
const generateSessionId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
