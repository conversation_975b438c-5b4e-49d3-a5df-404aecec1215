import { create } from 'zustand';
import { supabase, isSupabaseConfigured } from '../services/supabase';

// Import types from the original leads store
import type { Lead } from './leadsStore';

interface SupabaseLeadsState {
  leads: Lead[];
  isLoading: boolean;
  error: string | null;

  // Lead management
  addLead: (formId: string, formName: string, data: Record<string, any>) => Promise<void>;
  loadLeads: () => Promise<void>;
  deleteLead: (id: string) => Promise<void>;
  getLeadsByForm: (formId: string) => Lead[];

  // Clear state
  clearError: () => void;
}

export const useSupabaseLeadsStore = create<SupabaseLeadsState>((set, get) => ({
  leads: [],
  isLoading: false,
  error: null,

  addLead: async (formId: string, formName: string, data: Record<string, any>) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    set({ isLoading: true, error: null });

    try {
      const { data: submission, error } = await supabase
        .from('form_submissions')
        .insert({
          form_id: formId,
          form_name: formName,
          data: data,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const newLead: Lead = {
        id: submission.id,
        formId: submission.form_id,
        formName: submission.form_name,
        data: submission.data,
        submittedAt: new Date(submission.submitted_at),
      };

      set(state => ({
        leads: [newLead, ...state.leads],
        isLoading: false,
      }));

      // Track analytics event
      try {
        await supabase
          .from('analytics_events')
          .insert({
            form_id: formId,
            event_type: 'form_submission',
            session_id: getSessionId(),
            metadata: {
              submission_id: submission.id,
              form_name: formName,
            }
          });
      } catch (analyticsError) {
        console.warn('Failed to track analytics event:', analyticsError);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add lead';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  loadLeads: async () => {
    if (!isSupabaseConfigured()) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false });
        return;
      }

      // Get leads for forms owned by the current user
      const { data, error } = await supabase
        .from('form_submissions')
        .select(`
          *,
          forms!inner(user_id)
        `)
        .eq('forms.user_id', user.id)
        .order('submitted_at', { ascending: false });

      if (error) {
        throw error;
      }

      const leads: Lead[] = data.map(item => ({
        id: item.id,
        formId: item.form_id,
        formName: item.form_name,
        data: item.data,
        submittedAt: new Date(item.submitted_at),
      }));

      set({ leads, isLoading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load leads';
      set({ error: errorMessage, isLoading: false });
    }
  },

  deleteLead: async (id: string) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase
        .from('form_submissions')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      set(state => ({
        leads: state.leads.filter(lead => lead.id !== id),
        isLoading: false,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete lead';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  getLeadsByForm: (formId: string) => {
    return get().leads.filter(lead => lead.formId === formId);
  },

  clearError: () => {
    set({ error: null });
  },
}));

// Helper function to get or create session ID
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
};

// Helper function to generate session ID
const generateSessionId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
