import { create } from 'zustand';
import { supabase, isSupabaseConfigured } from '../services/supabase';

interface HighLevelConfig {
  privateIntegrationToken: string;
  locationId: string;
  defaultWorkflowId?: string;
}

interface Integration {
  id: string;
  provider: string;
  config: any;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface SupabaseIntegrationState {
  integrations: Integration[];
  isLoading: boolean;
  error: string | null;

  // Integration management
  loadIntegrations: () => Promise<void>;
  configureHighLevel: (config: HighLevelConfig) => Promise<void>;
  testHighLevelConnection: () => Promise<boolean>;
  syncLeadToHighLevel: (formData: Record<string, any>) => Promise<void>;
  disableIntegration: (provider: string) => Promise<void>;

  // Getters
  getHighLevelConfig: () => HighLevelConfig | null;
  isHighLevelEnabled: () => boolean;

  // Clear state
  clearError: () => void;
}

export const useSupabaseIntegrationStore = create<SupabaseIntegrationState>((set, get) => ({
  integrations: [],
  isLoading: false,
  error: null,

  loadIntegrations: async () => {
    if (!isSupabaseConfigured()) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false });
        return;
      }

      const { data, error } = await supabase
        .from('integrations')
        .select('*')
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      const integrations: Integration[] = data.map(item => ({
        id: item.id,
        provider: item.provider,
        config: item.config,
        enabled: item.enabled,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
      }));

      set({ integrations, isLoading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load integrations';
      set({ error: errorMessage, isLoading: false });
    }
  },

  configureHighLevel: async (config: HighLevelConfig) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Note: In a production environment, you would want to encrypt the config
      // before storing it in the database. For now, we'll store it as-is.
      // Consider using Supabase Edge Functions for server-side encryption.
      
      const { data, error } = await supabase
        .from('integrations')
        .upsert({
          user_id: user.id,
          provider: 'highlevel',
          config: config,
          enabled: true,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const integration: Integration = {
        id: data.id,
        provider: data.provider,
        config: data.config,
        enabled: data.enabled,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      set(state => ({
        integrations: state.integrations.some(i => i.provider === 'highlevel')
          ? state.integrations.map(i => i.provider === 'highlevel' ? integration : i)
          : [...state.integrations, integration],
        isLoading: false,
      }));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to configure HighLevel';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  testHighLevelConnection: async () => {
    const config = get().getHighLevelConfig();
    if (!config) {
      throw new Error('HighLevel not configured');
    }

    try {
      // Test the connection by making a simple API call
      const response = await fetch('https://services.leadconnectorhq.com/contacts/', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.privateIntegrationToken}`,
          'Content-Type': 'application/json',
        },
        // Add query parameters
        body: null,
      });

      if (response.status === 401) {
        throw new Error('Invalid HighLevel credentials');
      }

      if (!response.ok) {
        throw new Error(`HighLevel API error: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('HighLevel connection test failed:', error);
      throw error;
    }
  },

  syncLeadToHighLevel: async (formData: Record<string, any>) => {
    const config = get().getHighLevelConfig();
    if (!config) {
      throw new Error('HighLevel not configured');
    }

    try {
      // Extract contact information from form data
      const contact = {
        firstName: formData.firstName || formData.first_name || '',
        lastName: formData.lastName || formData.last_name || '',
        email: formData.email || '',
        phone: formData.phone || formData.phoneNumber || '',
        locationId: config.locationId,
        customFields: [],
        tags: ['StokeFlow Lead'],
      };

      // Validate required fields
      if (!contact.email && !contact.phone) {
        throw new Error('Either email or phone is required for HighLevel contact');
      }

      // Create contact in HighLevel
      const response = await fetch('https://services.leadconnectorhq.com/contacts/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.privateIntegrationToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contact),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`HighLevel API error: ${response.statusText} - ${errorData}`);
      }

      const result = await response.json();
      console.log('HighLevel contact created successfully:', result);

      // Add to default workflow if configured
      if (config.defaultWorkflowId && result.contact?.id) {
        await fetch(`https://services.leadconnectorhq.com/workflows/${config.defaultWorkflowId}/contacts/${result.contact.id}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.privateIntegrationToken}`,
            'Content-Type': 'application/json',
          },
        });
        console.log('Contact added to workflow:', config.defaultWorkflowId);
      }

      return result;
    } catch (error) {
      console.error('Error syncing lead to HighLevel:', error);
      throw error;
    }
  },

  disableIntegration: async (provider: string) => {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not configured');
    }

    set({ isLoading: true, error: null });

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('integrations')
        .update({ enabled: false })
        .eq('user_id', user.id)
        .eq('provider', provider);

      if (error) {
        throw error;
      }

      set(state => ({
        integrations: state.integrations.map(i =>
          i.provider === provider ? { ...i, enabled: false } : i
        ),
        isLoading: false,
      }));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to disable integration';
      set({ error: errorMessage, isLoading: false });
      throw error;
    }
  },

  getHighLevelConfig: () => {
    const integration = get().integrations.find(i => i.provider === 'highlevel' && i.enabled);
    return integration ? integration.config as HighLevelConfig : null;
  },

  isHighLevelEnabled: () => {
    const integration = get().integrations.find(i => i.provider === 'highlevel');
    return integration ? integration.enabled : false;
  },

  clearError: () => {
    set({ error: null });
  },
}));
