# Supabase Backend Setup for StokeFlow

This guide will help you set up Supabase as the backend for your StokeFlow application, replacing the current localStorage-based storage with a persistent, scalable database solution.

## Why Migrate to Supabase?

### Current Problems with localStorage:

- ❌ Data doesn't persist between devices/browsers
- ❌ No real user authentication
- ❌ HighLevel tokens stored in client-side (security risk)
- ❌ No analytics persistence
- ❌ No collaboration features
- ❌ Limited scalability

### Benefits of Supabase Backend:

- ✅ Persistent data storage across devices
- ✅ Real user authentication with email/password
- ✅ Secure server-side storage of integration tokens
- ✅ Real-time analytics and reporting
- ✅ Row-level security (RLS) for data protection
- ✅ Automatic backups and scaling
- ✅ API access for future integrations

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up for a free account
2. Click "New Project"
3. Choose your organization (or create one)
4. Fill in project details:
   - **Name**: `stokeflow-backend` (or your preferred name)
   - **Database Password**: Generate a strong password and save it
   - **Region**: Choose the region closest to your users
5. Click "Create new project"
6. Wait for the project to be created (usually takes 1-2 minutes)

## Step 2: Set Up the Database Schema

1. In your Supabase dashboard, go to the **SQL Editor**
2. Copy the entire contents of the `supabase-schema.sql` file from your project root
3. Paste it into the SQL Editor
4. Click **Run** to execute the schema

This will create:

- User profiles table
- Forms table with JSON storage for steps and settings
- Form submissions table for leads
- Integrations table for secure token storage
- Analytics tables for tracking and reporting
- Row-level security policies
- Automatic triggers for user creation and analytics

## Step 3: Configure Authentication

1. Go to **Authentication** > **Settings** in your Supabase dashboard
2. Under **Auth Providers**, ensure **Email** is enabled
3. Configure email settings:
   - **Enable email confirmations**: Recommended for production
   - **Enable email change confirmations**: Recommended
   - **Enable secure email change**: Recommended

### Optional: Configure Email Templates

1. Go to **Authentication** > **Email Templates**
2. Customize the email templates for:
   - Confirm signup
   - Reset password
   - Email change confirmation

## Step 4: Get Your Supabase Credentials

1. Go to **Settings** > **API** in your Supabase dashboard
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project-id.supabase.co`)
   - **anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## Step 5: Configure Environment Variables

1. Create a `.env.local` file in your project root (copy from `.env.example`)
2. Add your Supabase credentials:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

3. For production deployment on Netlify:
   - Go to your Netlify site settings
   - Navigate to **Environment variables**
   - Add the same variables there

## Step 6: Test the Connection

1. Start your development server: `npm run dev`
2. Open the application in your browser
3. Try to register a new account
4. Check your Supabase dashboard:
   - Go to **Authentication** > **Users** to see the new user
   - Go to **Table Editor** > **users** to see the user profile

## Step 7: Migrate Existing Data (Optional)

If you have existing forms and leads in localStorage:

1. Log in to your application with your new Supabase account
2. Open the browser console
3. Run the migration function:

```javascript
// Import the migration function
import { migrateToSupabase } from "./src/stores/storeManager";

// Run the migration
migrateToSupabase()
  .then(() => {
    console.log("Migration completed successfully!");
  })
  .catch((error) => {
    console.error("Migration failed:", error);
  });
```

## Step 8: Configure Row Level Security (RLS)

The schema automatically sets up RLS policies, but you can review and customize them:

1. Go to **Authentication** > **Policies** in your Supabase dashboard
2. Review the policies for each table:
   - Users can only see their own data
   - Forms are private to each user
   - Form submissions are linked to form owners
   - Integrations are private to each user

## Step 9: Set Up Real-time Subscriptions (Optional)

For real-time updates when forms are submitted:

1. Go to **Database** > **Replication** in your Supabase dashboard
2. Enable replication for the `form_submissions` table
3. The application will automatically receive real-time updates

## Step 10: Production Considerations

### Security

- ✅ Never expose your service role key in client-side code
- ✅ Use environment variables for all sensitive data
- ✅ Review and test RLS policies thoroughly
- ✅ Enable email confirmation for production

### Performance

- Consider adding database indexes for frequently queried fields
- Monitor query performance in the Supabase dashboard
- Use Supabase Edge Functions for complex server-side logic

### Backup

- Supabase automatically backs up your database
- Consider setting up additional backup strategies for critical data

## Troubleshooting

### Common Issues:

1. **"Supabase is not configured" error**

   - Check that your environment variables are set correctly
   - Restart your development server after adding variables

2. **Authentication errors**

   - Verify your Supabase URL and anon key
   - Check that email authentication is enabled in Supabase

3. **Permission denied errors**

   - Review RLS policies in your Supabase dashboard
   - Ensure the user is properly authenticated

4. **Migration issues**
   - Check browser console for detailed error messages
   - Verify that the database schema was created correctly

### Getting Help:

- Check the [Supabase documentation](https://supabase.com/docs)
- Review the browser console for error messages
- Check the Supabase dashboard logs
- Join the [Supabase Discord community](https://discord.supabase.com)

## Next Steps

Once Supabase is configured:

1. **Test all functionality**: Create forms, submit leads, check analytics
2. **Configure HighLevel integration**: Your tokens will now be stored securely
3. **Set up monitoring**: Use Supabase dashboard to monitor usage
4. **Plan for scaling**: Consider upgrading your Supabase plan as you grow

Your StokeFlow application now has a robust, scalable backend! 🚀
