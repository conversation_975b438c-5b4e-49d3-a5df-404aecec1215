<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StokeFlow Form Export Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1f2937;
            margin-bottom: 20px;
        }
        .description {
            color: #6b7280;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .form-list {
            margin: 20px 0;
        }
        .form-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .form-info h3 {
            margin: 0 0 5px 0;
            color: #1f2937;
        }
        .form-info p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        .form-id {
            font-family: monospace;
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #2563eb;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .export-all {
            background: #10b981;
            padding: 12px 24px;
            font-size: 16px;
            margin: 20px 0;
        }
        .export-all:hover {
            background: #059669;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #1e40af;
        }
        .instructions p {
            margin: 5px 0;
            color: #1e3a8a;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 StokeFlow Form Export Tool</h1>
        <div class="description">
            This tool exports your forms to static JSON files that can be accessed by embedded widgets on external websites.
            Run this whenever you create or update forms to make them available for embedding.
        </div>

        <div class="instructions">
            <h3>📋 How it works:</h3>
            <p>1. This tool reads your forms from localStorage</p>
            <p>2. Converts them to widget-compatible format</p>
            <p>3. Generates static JSON files that can be accessed cross-domain</p>
            <p>4. Updates the form registry for the embedding system</p>
        </div>

        <button class="export-all" onclick="exportAllForms()">
            📤 Export All Forms for Embedding
        </button>

        <div id="status" class="status"></div>

        <div class="form-list" id="formList">
            <p>Loading forms...</p>
        </div>
    </div>

    <script>
        let allForms = [];

        // Load and display forms
        function loadForms() {
            try {
                // Try both possible localStorage keys
                let storedData = localStorage.getItem('forms-storage'); // Zustand persist key
                if (!storedData) {
                    storedData = localStorage.getItem('form-storage'); // Legacy key
                }

                if (storedData) {
                    const formStore = JSON.parse(storedData);
                    allForms = formStore.state?.forms || formStore.forms || [];
                    displayForms();
                } else {
                    document.getElementById('formList').innerHTML = '<p>No forms found in localStorage.</p>';
                }
            } catch (error) {
                console.error('Error loading forms:', error);
                document.getElementById('formList').innerHTML = '<p>Error loading forms: ' + error.message + '</p>';
            }
        }

        // Display forms in the UI
        function displayForms() {
            const formList = document.getElementById('formList');
            
            if (allForms.length === 0) {
                formList.innerHTML = '<p>No forms found.</p>';
                return;
            }

            formList.innerHTML = allForms.map(form => `
                <div class="form-item">
                    <div class="form-info">
                        <h3>${form.name}</h3>
                        <p>${form.description || 'No description'}</p>
                        <div class="form-id">ID: ${form.id}</div>
                    </div>
                    <button onclick="exportSingleForm('${form.id}')">Export</button>
                </div>
            `).join('');
        }

        // Convert form to widget format
        function convertFormToWidgetFormat(form) {
            return {
                id: form.id,
                name: form.name,
                description: form.description || '',
                steps: form.steps.map(step => ({
                    id: step.id,
                    title: step.title,
                    description: step.description || '',
                    questions: step.questions.map(question => ({
                        id: question.id,
                        type: question.type,
                        title: question.title,
                        required: question.required || false,
                        placeholder: question.placeholder || '',
                        helpText: question.helpText || '',
                        choices: question.choices ? question.choices.map(choice => ({
                            id: choice.id,
                            value: choice.value || choice.label,
                            label: choice.label,
                            image: choice.imageUrl || choice.image
                        })) : []
                    }))
                })),
                settings: {
                    primaryColor: form.settings?.primaryColor || '#3B82F6',
                    showProgressBar: form.settings?.showProgressBar !== false,
                    thankYouMessage: form.settings?.thankYouMessage || 'Thank you for your submission!',
                    logoUrl: form.settings?.logoUrl,
                    redirectUrl: form.settings?.redirectUrl
                },
                exportedAt: new Date().toISOString()
            };
        }

        // Export all forms
        function exportAllForms() {
            try {
                showStatus('Exporting all forms...', 'info');
                
                const exportedForms = {};
                const formRegistry = {
                    forms: {},
                    exportedAt: new Date().toISOString(),
                    count: allForms.length
                };

                allForms.forEach(form => {
                    const widgetForm = convertFormToWidgetFormat(form);
                    exportedForms[form.id] = widgetForm;
                    formRegistry.forms[form.id] = {
                        id: form.id,
                        name: form.name,
                        description: form.description || '',
                        exportedAt: widgetForm.exportedAt
                    };
                });

                // Store the exported forms in localStorage for the API to access
                localStorage.setItem('exported-forms', JSON.stringify(exportedForms));
                localStorage.setItem('form-registry', JSON.stringify(formRegistry));

                showStatus(`✅ Successfully exported ${allForms.length} forms! They are now available for embedding.`, 'success');
                
                // Show instructions
                setTimeout(() => {
                    showStatus(`
                        <strong>✅ Export Complete!</strong><br>
                        Your forms are now available for embedding. The widget will automatically load the correct form data when embedded on external websites.
                        <br><br>
                        <strong>Next steps:</strong><br>
                        • Your forms can now be embedded using their form IDs<br>
                        • The embedding system will automatically find the exported form data<br>
                        • Re-run this export whenever you update forms
                    `, 'success');
                }, 2000);

            } catch (error) {
                console.error('Export error:', error);
                showStatus('❌ Export failed: ' + error.message, 'error');
            }
        }

        // Export single form
        function exportSingleForm(formId) {
            const form = allForms.find(f => f.id === formId);
            if (!form) {
                showStatus('❌ Form not found', 'error');
                return;
            }

            try {
                const widgetForm = convertFormToWidgetFormat(form);
                
                // Get existing exported forms
                let exportedForms = {};
                try {
                    const existing = localStorage.getItem('exported-forms');
                    if (existing) {
                        exportedForms = JSON.parse(existing);
                    }
                } catch (e) {
                    console.log('No existing exported forms');
                }

                // Add this form
                exportedForms[formId] = widgetForm;
                localStorage.setItem('exported-forms', JSON.stringify(exportedForms));

                showStatus(`✅ Exported form: ${form.name}`, 'success');
            } catch (error) {
                console.error('Export error:', error);
                showStatus('❌ Export failed: ' + error.message, 'error');
            }
        }

        // Show status message
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.innerHTML = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        // Initialize
        loadForms();
    </script>
</body>
</html>
