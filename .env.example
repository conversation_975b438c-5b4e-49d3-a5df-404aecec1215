# StokeFlow Environment Variables
# Copy this file to .env.local for local development
# Set these in Netlify environment variables for production

# Application Environment
VITE_APP_ENV=development

# Supabase Configuration
VITE_SUPABASE_URL=https://xphjuiimhjlixybhxbzs.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.oCCvqmw4fVav5-kTGluTVykNbDZzwq9z8d55E-tXFOI

# HighLevel CRM Integration (Optional)
# Get these from your HighLevel account settings
VITE_HIGHLEVEL_PRIVATE_TOKEN=pit-32f1a660-345d-4130-9df0-542cb3298c0b
VITE_HIGHLEVEL_LOCATION_ID=yBK5WOlszHMZB0udM7qC1
VITE_HIGHLEVEL_DEFAULT_WORKFLOW_ID=your_workflow_id_here

# Analytics Configuration
VITE_ANALYTICS_ENABLED=true

# Form Configuration
VITE_DEFAULT_FORM_COLOR=#3B82F6
VITE_MAX_FORMS_PER_USER=50

# Custom Domain (Production)
VITE_APP_DOMAIN=https://stokeflow.netlify.app

# Feature Flags
VITE_ENABLE_TEMPLATES=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_INTEGRATIONS=true
